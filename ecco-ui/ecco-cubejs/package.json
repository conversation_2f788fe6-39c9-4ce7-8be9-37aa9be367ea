{"private": true, "name": "ecco-cubejs", "version": "0.0.0", "main": "./build-tsc/index.js", "typings": "./build-tsc/index.d.ts", "scripts": {"clean": "tsc --build --clean", "emit": "webpack --config-name=dev && eslint --ext .ts .", "build": "eslint --ext .ts . && webpack", "lint": "eslint --ext .ts .", "test": "echo && npm run cy:test", "cy:test": "echo Nothing to do", "cy:dev": "cypress open-ct"}, "dependencies": {"@cubejs-client/core": "^0.35.0", "@cubejs-client/react": "^0.35.0", "@cubejs-client/ws-transport": "^0.35.23", "react-chartjs-2": "^5.2.0", "chart.js": "^4.4.2", "react": "16.13.1", "react-router": "^5.2.0", "react-dom": "16.13.1"}, "peerDependencies": {}, "devDependencies": {"@testing-library/react": "^12.1.4", "@bahmutov/cypress-esbuild-preprocessor": "^2.2.0", "@cypress/react": "^5.12.4", "@cypress/webpack-dev-server": "^1.8.3", "@testing-library/dom": "^8.11.3", "@testing-library/cypress": "^8.0.1", "@typescript-eslint/eslint-plugin": "^4.9.0", "@typescript-eslint/parser": "^4.9.0", "babel-loader": "^8.0.6", "cypress": "^9.5.2", "esbuild": "^0.17.19", "esbuild-loader": "^2.21.0", "eslint": "^7.14.0", "fake-indexeddb": "^3.1.7", "shx": "0.3.2", "terser-webpack-plugin": "^2.2.1", "ts-loader": "^5.3.3", "typescript": "5.1.5", "webpack": "^4.42.1", "webpack-cli": "^3.2.3"}}