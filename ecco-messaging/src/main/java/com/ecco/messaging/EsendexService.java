package com.ecco.messaging;

import esendex.sdk.java.ServiceFactory;
import esendex.sdk.java.model.domain.request.SmsMessageRequest;
import esendex.sdk.java.model.domain.response.MessageResultResponse;
import esendex.sdk.java.service.BasicServiceFactory;
import esendex.sdk.java.service.MessagingService;
import esendex.sdk.java.service.auth.UserPassword;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Nonnull;
import javax.annotation.PostConstruct;

import java.util.Objects;

import static org.springframework.util.StringUtils.hasText;


@SuppressWarnings("SpringElInspection")
@ConditionalOnProperty(value = "ecco.messaging.esendex.enabled", havingValue = "true")
@Component
@Slf4j
public class EsendexService {

    // using the Java SDK goes via the user account, not API
    // see https://developers-portal.esendex.com/default/documentation/conversations#/Conversations/v1%2Fmessages
    /*private static String baseUrl = "https://conversations.esendex.com";
    private static String smsUrl = "/v1/messages";
    @Value("#{environment.ESENDEX_APIKEY}")
    private String APIKEY;*/

    @Value("#{environment.ESENDEX_ACCOUNT_REFERENCE}")
    private String ACCOUNT_REFERENCE;

    @Value("#{environment.ESENDEX_USERNAME}")
    private String USERNAME;
    @Value("#{environment.ESENDEX_PASSWORD}")
    private String PASSWORD;

    private MessagingService messagingService;

    @PostConstruct
    public void init() {
        if (hasText(ACCOUNT_REFERENCE)) {
            UserPassword userPassword = new UserPassword(USERNAME,PASSWORD);
            BasicServiceFactory serviceFactory = ServiceFactory.createBasicAuthenticatingFactory(userPassword);
            this.messagingService = serviceFactory.getMessagingService();
        }
    }

    public @Nonnull MessageResult sendSms(String toMobileNumber, String smsBody) {
        if (!hasText(ACCOUNT_REFERENCE)) {
            log.warn("Esendex not configured. Cannot send SMS: {}\nto {}", smsBody, toMobileNumber);
            return MessageResult.failed(toMobileNumber, "Outbound SMS not configured. Message not really sent.");
        }

        // mobile number in format '+{Country Dial Code}{Rest of phone number}' - https://en.wikipedia.org/wiki/MSISDN#MSISDN_Format
        var toMobileNumberClean = StringUtils.trimAllWhitespace(toMobileNumber);
        log.debug("Submitting SMS message");// Unique resource ID created to manage this transaction

        SmsMessageRequest message = new SmsMessageRequest(toMobileNumberClean, smsBody);
        MessageResultResponse response;
        try {
            response = messagingService.sendMessage(ACCOUNT_REFERENCE, message);
        } catch (Exception e) {
            //e.getStackTrace();
            return MessageResult.error(toMobileNumberClean, e);
        }

        log.debug("Submitted SMS message: {}", response.getBatchId());
        //log.debug("status: {}", response.getMessageIds());

        return MessageResult.success(toMobileNumberClean);
    }

    public void validateAccount(String accountId) {
        Assert.state(Objects.equals(accountId, ACCOUNT_REFERENCE), "Invalid inbound account for webhook");
    }

}
