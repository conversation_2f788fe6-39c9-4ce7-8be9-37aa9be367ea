body {
    font-family: arial, sans-serif;
    font-weight: normal;
    color: #404040;
    background-color: #FFF;
}

#site-title {
    position: absolute;
    top: 12px;
    left: 80px;
}

.button, .buttonBlur {
    font-family: arial, sans-serif;
    font-weight: normal;
}
.iehoveron {
    font-family: arial, sans-serif;
    font-weight: normal;
}

.iehoveron {
    border-color: #D0D0D0;
    color: #FFF;
    background: #D0D0D0;
}

.list-builder.js .selected-items li {
    background: url('../themes/ecco/images/reorder-handle.png') no-repeat;
}

/* global 'a' links */
/* also repeated around adapting jquery ui below */

a:link, a:visited
{
    color: #3c83ca;
}
a:link:hover, a:visited:hover
{
    color: #F7F6F6;
    background: #3c83ca;
}
a.active, a:active
{
    color: #F7F6F6;
    background: #3c83ca;
}

/* content area - implied or explicit */

span.tooltip:hover span.tip, .ietipson {
  color: #404040;
  border-color: #404040;
}

.input_readonly {
    border: none;
    font-family: arial, sans-serif;
    background: #FBFAFA;
    font-weight: normal;
}

.iehoveron {
    border-color: #3c83ca;
    color: #F7F6F6;
    background: #3c83ca;
}

.errortext, .input-status-error {
    color: #CD0A01;
}
.messageText {
    color: #878787;
}
.clearFind a, .add a {
    background-color: #fff;
}
.clearFind a:hover, .add a:hover {
    background-color: #3c83ca;
}


/* tips */
.appendDateTime {
    cursor: pointer;
}

.view-header {
    border-color: #A2B7D8;
    border-left: 0;
    border-right: 0;
    border-top: 0;
}

.ietipson, span.tooltip:hover span.tip, #dhtmltooltip {
    background-color: lightyellow;
}



/* fullcalendar */
.fc-event, .fc-agenda .fc-event-time, .fc-event a {
    background-color: #fff;    /* #FBFAFA */
    border-color: #D0D0D0;
    color: #3c83ca;
}

/* JQUERY UI FIXES */
/* reinstate the correct colour for our links after tabs change it */
/* (we need 'div' here to gain precedence over the subsequently loaded jqueryui.css) */
div.ui-widget-content a {
    color: #3c83ca;
}
/* prevent the tabs having backgrounds - they look ugly */
/* (we need 'ul' here to gain precedence over the subsequently loaded jqueryui.css) */
ul.ui-widget-header {
    border: none; background: none transparent;
}
.ui-state-hover a:link:hover,
.ui-state-hover a:link:visited {
    background: none transparent;
    color: #3c83ca;
}
/* JQUERY UI FIXES - END */
