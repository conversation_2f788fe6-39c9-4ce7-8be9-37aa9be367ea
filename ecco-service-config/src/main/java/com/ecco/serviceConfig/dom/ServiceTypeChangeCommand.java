package com.ecco.serviceConfig.dom;

import org.joda.time.Instant;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import java.util.UUID;

@Entity
@DiscriminatorValue("servicetypechange")
public class ServiceTypeChangeCommand extends ServiceTypeCommand {

    /**
     * @deprecated Do not use. Required by JPA/Hibernate.
     */
    @Deprecated
    public ServiceTypeChangeCommand() {
        super();
    }

    public ServiceTypeChangeCommand(@Nullable UUID uuid, @Nonnull Instant remoteCreationTime,
                                    long userId, @Nonnull String body) {
        super(uuid, remoteCreationTime, userId, body);
    }

}
