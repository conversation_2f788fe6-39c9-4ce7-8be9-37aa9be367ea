package com.ecco.webApi.notifications;

import com.ecco.dom.notifications.NotificationRecipient;
import com.ecco.repositories.notifications.NotificationRepository;
import com.ecco.security.SecurityUtil;
import com.ecco.security.dom.User;
import com.ecco.webApi.viewModels.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Nonnull;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Controller for managing user notifications
 */
@RestController
@RequestMapping("/notifications")
public class NotificationController {

    private static final Logger log = LoggerFactory.getLogger(NotificationController.class);

    private final NotificationRepository notificationRepository;
    private final NotificationToViewModel notificationToViewModel;

    @Autowired
    public NotificationController(
            NotificationRepository notificationRepository,
            NotificationToViewModel notificationToViewModel) {
        this.notificationRepository = notificationRepository;
        this.notificationToViewModel = notificationToViewModel;
    }

    /**
     * Get notifications for the current user
     */
    @GetJson("/")
    public List<NotificationViewModel> getCurrentUserNotifications(
            @Nonnull Authentication auth,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "true") boolean unreadOnly) {

        User user = SecurityUtil.getUser(auth);

        // Create a pageable request with sorting by creation date (newest first)
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "created"));

        // Get notifications for the current user
        Iterable<NotificationRecipient> notifications;
        if (unreadOnly) {
            notifications = notificationRepository.findByUserIdAndReadAtIsNull(user.getId(), pageable);
        } else {
            notifications = notificationRepository.findByUserId(user.getId(), pageable);
        }

        return StreamSupport.stream(notifications.spliterator(), false)
                .map(notificationToViewModel)
                .collect(Collectors.toList());
    }

    /**
     * Get a specific notification
     */
    @GetJson("/uuid/{commandUuid}")
    public NotificationViewModel getNotification(
            @Nonnull Authentication auth,
            @PathVariable UUID commandUuid) {

        User user = SecurityUtil.getUser(auth);

        // Find the notification
        NotificationRecipient notification = notificationRepository
                .findByCommandUuidAndUserId(commandUuid, user.getId())
                .orElseThrow(() -> new IllegalArgumentException("Notification not found for " + commandUuid + " and user " + user.getId()));

        // Convert to view model
        return notificationToViewModel.apply(notification);
    }

    /**
     * Get the count of unread notifications for the current user
     */
    @GetJson("/count-unread")
    public long countUnread(@Nonnull Authentication auth) {
        User user = SecurityUtil.getUser(auth);
        return notificationRepository.countByUserIdAndReadAtIsNull(user.getId());
    }

    /**
     * Mark a notification as read
     */
    @PostJson("/uuid/{commandUuid}/read")
    public Result markAsRead(
            @Nonnull Authentication auth,
            @PathVariable UUID commandUuid) {

        User user = SecurityUtil.getUser(auth);

        // Find the notification
        NotificationRecipient notification = notificationRepository
                .findByCommandUuidAndUserId(commandUuid, user.getId())
                .orElseThrow(() -> new IllegalArgumentException("Notification not found for " + commandUuid + " and user " + user.getId()));

        // Mark as read if not already read
        if (notification.getReadAt() == null) {
            notification.setReadAt(Instant.now());
            notificationRepository.save(notification);
        }

        return new Result("notification marked as read");
    }

    /**
     * Mark all notifications as read for the current user
     */
    @PostJson("/read-all")
    public Result markAllAsRead(@Nonnull Authentication auth) {
        User user = SecurityUtil.getUser(auth);

        // Find all unread notifications for the user
        Iterable<NotificationRecipient> unreadNotifications =
                notificationRepository.findByUserIdAndReadAtIsNull(user.getId());

        // Mark all as read
        Instant now = Instant.now();
        int count = 0;
        for (NotificationRecipient notification : unreadNotifications) {
            notification.setReadAt(now);
            notificationRepository.save(notification);
            count++;
        }

        return new Result(count + " notifications marked as read");
    }
}
