package com.ecco.event;

import org.springframework.context.ApplicationEvent;

import java.io.Serializable;

public class CacheEvictEvent extends ApplicationEvent {

    private static final long serialVersionUID = 1L;

    private final String cacheName;
    private final Serializable identifier;

    public CacheEvictEvent(Object source, String cacheName, Serializable identifier) {
        super(source);
        this.cacheName = cacheName;
        this.identifier = identifier;
    }

    public String getCacheName() {
        return cacheName;
    }

    public Serializable getIdentifier() {
        return identifier;
    }

}
