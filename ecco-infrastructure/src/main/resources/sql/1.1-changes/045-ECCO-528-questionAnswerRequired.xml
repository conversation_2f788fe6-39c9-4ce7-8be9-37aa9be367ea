<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.0.xsd">

    <changeSet id="ECCO-528-questionanswerrequired-v2" author="adamjhamer">
        <preConditions onFail="MARK_RAN">
            <not>
                <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/045-ECCO-528-questionAnswerRequired.xml"
                        author="adamjhamer" id="ECCO-528-questionanswerrequired"/>
            </not>
        </preConditions>
        <addColumn tableName="questions">
            <column name="answerrequired" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- if we did run the original, add the not null to it -->
    <changeSet author="nealeu" id="ECCO-528-questionanswerrequired-fix">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/045-ECCO-528-questionAnswerRequired.xml"
                    author="adamjhamer" id="ECCO-528-questionanswerrequired"/>
        </preConditions>
        <addNotNullConstraint tableName="questions" columnName="answerrequired" columnDataType="BOOLEAN"/>
    </changeSet>

    <!-- if we did run the original, add the default value to it -->
    <changeSet author="adamjhamer" id="DEV-122-questionanswerrequired-fix-fix">
        <preConditions onFail="MARK_RAN">
            <changeSetExecuted changeLogFile="classpath:sql/1.1-changes/045-ECCO-528-questionAnswerRequired.xml"
                               author="adamjhamer" id="ECCO-528-questionanswerrequired"/>
        </preConditions>
        <addDefaultValue tableName="questions" columnName="answerrequired" defaultValueBoolean="false"/>
    </changeSet>

</databaseChangeLog>
